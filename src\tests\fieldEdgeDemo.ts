/**
 * 字段级连线功能演示
 */

import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data } from '@/utils/graphDataTransform'

/**
 * 演示字段级连线功能
 */
export function demonstrateFieldEdgeFeatures() {
  console.log('🎯 字段级连线功能演示')
  console.log('=' .repeat(60))
  
  // 1. 创建示例数据
  console.log('📊 1. 创建示例血缘数据')
  const lineageData = createSampleLineageData()
  
  console.log(`   - 表数量: ${Object.keys(lineageData.tables).length}`)
  console.log(`   - 字段数量: ${lineageData.nodes.length}`)
  console.log(`   - 连接数量: ${lineageData.edges.length}`)
  
  // 2. 显示原始边数据
  console.log('\n🔗 2. 原始字段级连接关系')
  lineageData.edges.forEach((edge, index) => {
    console.log(`   ${index + 1}. ${edge.source} → ${edge.target}`)
    console.log(`      类型: ${edge.transformType} | 置信度: ${edge.confidence} | 标签: ${edge.label}`)
    if (edge.expression) {
      console.log(`      表达式: ${edge.expression}`)
    }
  })
  
  // 3. 转换为G6数据
  console.log('\n⚙️ 3. 转换为G6图数据')
  const g6Data = transformToG6Data(lineageData)
  
  console.log(`   - 节点数量: ${g6Data.nodes.length}`)
  console.log(`   - 边数量: ${g6Data.edges.length}`)
  
  // 4. 显示转换后的边数据
  console.log('\n🎨 4. 转换后的边样式配置')
  g6Data.edges.forEach((edge, index) => {
    console.log(`   ${index + 1}. ${edge.source} → ${edge.target}`)
    console.log(`      字段连接: ${edge.sourceField} → ${edge.targetField}`)
    console.log(`      边类型: ${edge.type}`)
    console.log(`      转换类型: ${edge.transformType}`)
    console.log(`      样式配置:`, {
      stroke: edge.style?.stroke,
      lineWidth: edge.style?.lineWidth,
      lineDash: edge.style?.lineDash
    })
  })
  
  // 5. 显示节点信息
  console.log('\n📋 5. 表节点信息')
  g6Data.nodes.forEach((node, index) => {
    console.log(`   ${index + 1}. 表: ${node.tableName}`)
    console.log(`      字段数量: ${node.fields.length}`)
    console.log(`      字段列表: ${node.fields.map(f => f.fieldName).join(', ')}`)
  })
  
  // 6. 验证字段级连接的完整性
  console.log('\n✅ 6. 验证字段级连接完整性')
  let allValid = true
  
  g6Data.edges.forEach((edge, index) => {
    const sourceNode = g6Data.nodes.find(n => n.tableName === edge.source)
    const targetNode = g6Data.nodes.find(n => n.tableName === edge.target)
    
    if (!sourceNode || !targetNode) {
      console.error(`   ❌ 边 ${index + 1}: 找不到对应的表节点`)
      allValid = false
      return
    }
    
    const sourceFieldExists = sourceNode.fields.some(f => f.fieldName === edge.sourceField)
    const targetFieldExists = targetNode.fields.some(f => f.fieldName === edge.targetField)
    
    if (!sourceFieldExists || !targetFieldExists) {
      console.error(`   ❌ 边 ${index + 1}: 字段不存在`)
      allValid = false
    } else {
      console.log(`   ✅ 边 ${index + 1}: ${edge.source}.${edge.sourceField} → ${edge.target}.${edge.targetField}`)
    }
  })
  
  console.log('\n' + '=' .repeat(60))
  console.log(`🎉 演示完成! 字段级连线功能 ${allValid ? '✅ 正常工作' : '❌ 存在问题'}`)
  
  return {
    success: allValid,
    lineageData,
    g6Data,
    summary: {
      tables: Object.keys(lineageData.tables).length,
      fields: lineageData.nodes.length,
      connections: lineageData.edges.length,
      nodes: g6Data.nodes.length,
      edges: g6Data.edges.length
    }
  }
}

/**
 * 显示字段级连线的技术特性
 */
export function showFieldEdgeTechnicalFeatures() {
  console.log('🔬 字段级连线技术特性展示')
  console.log('=' .repeat(60))
  
  console.log('📌 1. 支持的转换类型及样式:')
  const transformTypes = [
    { type: 'DIRECT', color: '#1890ff', dash: '实线', desc: '直接映射' },
    { type: 'JOIN', color: '#52c41a', dash: '实线', desc: '连接操作' },
    { type: 'AGGREGATE', color: '#fa8c16', dash: '实线', desc: '聚合函数' },
    { type: 'FILTER', color: '#722ed1', dash: '5,5', desc: '过滤条件' },
    { type: 'TRANSFORM', color: '#eb2f96', dash: '3,3', desc: '数据转换' },
    { type: 'UNION', color: '#13c2c2', dash: '8,4', desc: '联合操作' },
    { type: 'WINDOW', color: '#faad14', dash: '2,2,8,2', desc: '窗口函数' }
  ]
  
  transformTypes.forEach(item => {
    console.log(`   ${item.type.padEnd(10)} | ${item.color} | ${item.dash.padEnd(8)} | ${item.desc}`)
  })
  
  console.log('\n📌 2. 置信度与线宽映射:')
  const confidenceMappings = [
    { confidence: '≥ 0.9', width: '3px', desc: '高置信度' },
    { confidence: '≥ 0.7', width: '2px', desc: '中等置信度' },
    { confidence: '≥ 0.5', width: '1.5px', desc: '低置信度' },
    { confidence: '< 0.5', width: '1px', desc: '极低置信度' }
  ]
  
  confidenceMappings.forEach(item => {
    console.log(`   ${item.confidence.padEnd(6)} | ${item.width.padEnd(5)} | ${item.desc}`)
  })
  
  console.log('\n📌 3. 核心技术实现:')
  const features = [
    '✅ 自定义FieldEdge类继承BaseEdge',
    '✅ 字段ID解析 (表名.字段名)',
    '✅ 动态样式配置 (颜色、线宽、虚线)',
    '✅ 贝塞尔曲线路径生成',
    '✅ 端点箭头动画效果',
    '✅ 标签背景和定位',
    '✅ 完整的类型定义支持',
    '✅ 单元测试覆盖'
  ]
  
  features.forEach(feature => {
    console.log(`   ${feature}`)
  })
  
  console.log('\n' + '=' .repeat(60))
  console.log('🎯 字段级连线功能已完全实现，支持企业级数据血缘分析需求!')
}

/**
 * 运行完整演示
 */
export function runCompleteDemo() {
  const demoResult = demonstrateFieldEdgeFeatures()
  console.log('\n')
  showFieldEdgeTechnicalFeatures()
  
  return demoResult
}
