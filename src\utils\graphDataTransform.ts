/**
 * 图数据转换工具
 * 用于将原始血缘数据转换为G6可用的节点和边数据
 */

import type {
  LineageData,
  G6GraphData,
  G6NodeData,
  G6EdgeData,
  TableInfo,
  LineageNode
} from '@/types/lineage'

/**
 * 将血缘数据转换为G6图数据
 * @param lineageData 原始血缘数据
 * @returns G6图数据
 */
export function transformToG6Data(lineageData: LineageData): G6GraphData {
  const nodes: G6NodeData[] = []
  const edges: G6EdgeData[] = []

  // 转换表节点
  Object.values(lineageData.tables).forEach((table: TableInfo) => {
    const nodeData: G6NodeData = {
      id: table.name,
      type: 'table-node',
      x: table.position?.x,
      y: table.position?.y,
      tableName: table.name,
      fields: table.fields,
      tableInfo: table,
      label: table.name,
      labelCfg: {
        position: 'top',
        offset: 10
      }
    }
    nodes.push(nodeData)
  })

  // 转换边数据 - 支持字段级连接
  lineageData.edges.forEach((edge) => {
    const sourceTableName = getTableNameFromFieldId(edge.source)
    const targetTableName = getTableNameFromFieldId(edge.target)
    const sourceFieldName = getFieldNameFromFieldId(edge.source)
    const targetFieldName = getFieldNameFromFieldId(edge.target)

    const edgeData: G6EdgeData = {
      id: edge.id,
      source: sourceTableName,
      target: targetTableName,
      type: 'field-edge',
      lineageEdge: edge,
      label: edge.label,
      // 添加字段级连接的额外信息
      sourceField: sourceFieldName,
      targetField: targetFieldName,
      sourceFieldId: edge.source,
      targetFieldId: edge.target,
      transformType: edge.transformType,
      confidence: edge.confidence,
      expression: edge.expression,
      style: {
        // 根据转换类型设置不同的样式
        stroke: getEdgeColorByTransformType(edge.transformType),
        lineWidth: getEdgeWidthByConfidence(edge.confidence),
        lineDash: getEdgeDashByTransformType(edge.transformType)
      },
      labelCfg: {
        autoRotate: true,
        style: {
          fontSize: 10,
          fill: '#666',
          background: {
            fill: '#ffffff',
            padding: [2, 4],
            radius: 2
          }
        }
      }
    }
    edges.push(edgeData)
  })

  return { nodes, edges }
}

/**
 * 从字段ID中提取表名
 * @param fieldId 字段ID，格式：表名.字段名
 * @returns 表名
 */
export function getTableNameFromFieldId(fieldId: string): string {
  return fieldId.split('.')[0]
}

/**
 * 从字段ID中提取字段名
 * @param fieldId 字段ID，格式：表名.字段名
 * @returns 字段名
 */
export function getFieldNameFromFieldId(fieldId: string): string {
  return fieldId.split('.').slice(1).join('.')
}

/**
 * 生成字段ID
 * @param tableName 表名
 * @param fieldName 字段名
 * @returns 字段ID
 */
export function generateFieldId(tableName: string, fieldName: string): string {
  return `${tableName}.${fieldName}`
}

/**
 * 计算表节点的尺寸
 * @param fields 字段列表
 * @returns 节点尺寸 [width, height]
 */
export function calculateNodeSize(fields: LineageNode[]): [number, number] {
  const minWidth = 200
  const minHeight = 100
  const fieldHeight = 24
  const headerHeight = 40
  const padding = 16

  // 计算最长字段名的宽度（简单估算）
  const maxFieldNameLength = Math.max(
    ...fields.map(field => field.fieldName.length)
  )
  const estimatedWidth = Math.max(minWidth, maxFieldNameLength * 8 + padding * 2)

  // 计算高度
  const estimatedHeight = Math.max(
    minHeight,
    headerHeight + fields.length * fieldHeight + padding
  )

  return [estimatedWidth, estimatedHeight]
}

/**
 * 根据字段类型获取颜色
 * @param dataType 数据类型
 * @returns 颜色值
 */
export function getFieldTypeColor(dataType?: string): string {
  if (!dataType) return '#666'

  const type = dataType.toLowerCase()

  if (type.includes('int') || type.includes('number') || type.includes('decimal')) {
    return '#1890ff' // 蓝色 - 数值类型
  }
  if (type.includes('varchar') || type.includes('char') || type.includes('text')) {
    return '#52c41a' // 绿色 - 字符类型
  }
  if (type.includes('date') || type.includes('time') || type.includes('timestamp')) {
    return '#fa8c16' // 橙色 - 时间类型
  }
  if (type.includes('bool') || type.includes('bit')) {
    return '#eb2f96' // 粉色 - 布尔类型
  }

  return '#666' // 默认灰色
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID
 */
export function generateUniqueId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 根据转换类型获取边颜色
 * @param transformType 转换类型
 * @returns 颜色值
 */
export function getEdgeColorByTransformType(transformType?: string): string {
  const colorMap: Record<string, string> = {
    'DIRECT': '#1890ff',      // 直接映射 - 蓝色
    'JOIN': '#52c41a',        // 连接 - 绿色
    'AGGREGATE': '#fa8c16',   // 聚合 - 橙色
    'FILTER': '#722ed1',      // 过滤 - 紫色
    'TRANSFORM': '#eb2f96',   // 转换 - 粉色
    'UNION': '#13c2c2',       // 联合 - 青色
    'WINDOW': '#faad14'       // 窗口函数 - 黄色
  }

  return colorMap[transformType || 'DIRECT'] || '#1890ff'
}

/**
 * 根据置信度获取边宽度
 * @param confidence 置信度 (0-1)
 * @returns 线宽
 */
export function getEdgeWidthByConfidence(confidence?: number): number {
  if (confidence === undefined) return 2

  if (confidence >= 0.9) return 3
  if (confidence >= 0.7) return 2
  if (confidence >= 0.5) return 1.5
  return 1
}

/**
 * 根据转换类型获取边虚线样式
 * @param transformType 转换类型
 * @returns 虚线数组
 */
export function getEdgeDashByTransformType(transformType?: string): number[] | undefined {
  const dashMap: Record<string, number[]> = {
    'DIRECT': [],             // 实线
    'JOIN': [],               // 实线
    'AGGREGATE': [],          // 实线
    'FILTER': [5, 5],         // 虚线
    'TRANSFORM': [3, 3],      // 短虚线
    'UNION': [8, 4],          // 长虚线
    'WINDOW': [2, 2, 8, 2]    // 点划线
  }

  const dash = dashMap[transformType || 'DIRECT']
  return dash && dash.length > 0 ? dash : undefined
}

/**
 * 深度克隆对象
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func.apply(null, args)
    }, wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  let previous = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    const remaining = wait - (now - previous)

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      func.apply(null, args)
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now()
        timeout = null
        func.apply(null, args)
      }, remaining)
    }
  }
}
