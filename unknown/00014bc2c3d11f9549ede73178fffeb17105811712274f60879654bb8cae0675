/**
 * 字段级连线功能测试
 */

import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data } from '@/utils/graphDataTransform'
import { registerAllGraphElements } from '@/utils/registerFieldNode'

/**
 * 测试字段级连线数据转换
 */
export function testFieldEdgeDataTransform() {
  console.log('=== 测试字段级连线数据转换 ===')
  
  // 创建示例数据
  const lineageData = createSampleLineageData()
  console.log('原始血缘数据:', lineageData)
  
  // 转换为G6数据
  const g6Data = transformToG6Data(lineageData)
  console.log('转换后的G6数据:', g6Data)
  
  // 验证边数据
  g6Data.edges.forEach((edge, index) => {
    console.log(`边 ${index + 1}:`, {
      id: edge.id,
      source: edge.source,
      target: edge.target,
      type: edge.type,
      sourceField: edge.sourceField,
      targetField: edge.targetField,
      transformType: edge.transformType,
      confidence: edge.confidence,
      label: edge.label
    })
  })
  
  return g6Data
}

/**
 * 测试图形元素注册
 */
export function testGraphElementsRegistration() {
  console.log('=== 测试图形元素注册 ===')
  
  try {
    registerAllGraphElements()
    console.log('✅ 图形元素注册成功')
    return true
  } catch (error) {
    console.error('❌ 图形元素注册失败:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
export function runFieldEdgeTests() {
  console.log('🚀 开始字段级连线功能测试')
  
  // 测试1: 图形元素注册
  const registrationSuccess = testGraphElementsRegistration()
  
  // 测试2: 数据转换
  const g6Data = testFieldEdgeDataTransform()
  
  // 测试结果总结
  console.log('📊 测试结果总结:')
  console.log(`- 图形元素注册: ${registrationSuccess ? '✅ 成功' : '❌ 失败'}`)
  console.log(`- 数据转换: ${g6Data ? '✅ 成功' : '❌ 失败'}`)
  console.log(`- 边数量: ${g6Data?.edges.length || 0}`)
  console.log(`- 节点数量: ${g6Data?.nodes.length || 0}`)
  
  return {
    registrationSuccess,
    dataTransformSuccess: !!g6Data,
    edgeCount: g6Data?.edges.length || 0,
    nodeCount: g6Data?.nodes.length || 0
  }
}
