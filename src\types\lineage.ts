/**
 * 数据血缘图相关类型定义
 */

// 数据库类型枚举
export enum DatabaseType {
  MYSQL = 'mysql',
  POSTGRESQL = 'postgresql',
  ORACLE = 'oracle',
  SQLSERVER = 'sqlserver',
  HIVE = 'hive',
  SPARK = 'spark'
}

// 字段数据类型
export interface FieldDataType {
  type: string;
  length?: number;
  precision?: number;
  scale?: number;
}

// 血缘节点接口
export interface LineageNode {
  id: string;           // 格式：表名.字段名
  label: string;        // 字段显示名称
  tableName: string;    // 所属表名
  fieldName: string;    // 字段名
  dataType?: FieldDataType; // 数据类型
  description?: string; // 字段描述
  x?: number;          // 节点X坐标
  y?: number;          // 节点Y坐标
  type: 'field';       // 节点类型
  isKey?: boolean;     // 是否为主键
  isNullable?: boolean; // 是否可为空
  defaultValue?: string; // 默认值
}

// 血缘边接口
export interface LineageEdge {
  id: string;           // 边的唯一标识
  source: string;       // 源字段ID
  target: string;       // 目标字段ID
  label?: string;       // 关系类型（如：Mapped, Aggregated）
  transformType?: string; // 转换类型
  expression?: string;  // SQL表达式
  confidence?: number;  // 置信度 0-1
}

// 表信息接口
export interface TableInfo {
  name: string;         // 表名
  schema?: string;      // 模式名
  database?: string;    // 数据库名
  type: 'table' | 'view' | 'temp'; // 表类型
  fields: LineageNode[]; // 字段列表
  position?: { x: number; y: number }; // 表在图中的位置
  description?: string; // 表描述
  rowCount?: number;    // 行数
  size?: string;        // 表大小
}

// 血缘数据接口
export interface LineageData {
  nodes: LineageNode[];
  edges: LineageEdge[];
  tables: {
    [tableName: string]: TableInfo;
  };
  metadata?: {
    sqlText?: string;     // 原始SQL
    parseTime?: string;   // 解析时间
    version?: string;     // 版本信息
  };
}

// G6节点数据接口
export interface G6NodeData {
  id: string;
  type?: string;
  x?: number;
  y?: number;
  size?: number | [number, number];
  style?: Record<string, any>;
  label?: string;
  labelCfg?: Record<string, any>;
  // 自定义数据
  tableName: string;
  fields: LineageNode[];
  tableInfo: TableInfo;
  // 允许任意字符串索引
  [key: string]: any;
}

// G6边数据接口
export interface G6EdgeData {
  id: string;
  source: string;
  target: string;
  type?: string;
  style?: Record<string, any>;
  label?: string;
  labelCfg?: Record<string, any>;
  // 自定义数据
  lineageEdge: LineageEdge;
  // 字段级连接的额外信息
  sourceField?: string;        // 源字段名
  targetField?: string;        // 目标字段名
  sourceFieldId?: string;      // 源字段ID
  targetFieldId?: string;      // 目标字段ID
  transformType?: string;      // 转换类型
  confidence?: number;         // 置信度
  expression?: string;         // SQL表达式
  // 允许任意字符串索引
  [key: string]: any;
}

// G6图数据接口
export interface G6GraphData {
  nodes: G6NodeData[];
  edges: G6EdgeData[];
}

// 图谱配置接口
export interface GraphConfig {
  width: number;
  height: number;
  fitView: boolean;
  fitViewPadding: number | [number, number, number, number];
  animate: boolean;
  animateCfg: {
    duration: number;
    easing: string;
  };
  modes: {
    default: string[];
  };
  layout: {
    type: string;
    rankdir: string;
    align: string;
    nodesep: number;
    ranksep: number;
  };
  defaultNode: Record<string, any>;
  defaultEdge: Record<string, any>;
  nodeStateStyles: Record<string, any>;
  edgeStateStyles: Record<string, any>;
}

// 搜索结果接口
export interface SearchResult {
  type: 'table' | 'field';
  id: string;
  name: string;
  tableName?: string;
  description?: string;
  matchScore: number;
}

// 路径追踪结果接口
export interface PathTraceResult {
  path: string[];       // 路径节点ID数组
  edges: string[];      // 路径边ID数组
  direction: 'upstream' | 'downstream' | 'both';
  depth: number;        // 追踪深度
}

// 导出配置接口
export interface ExportConfig {
  format: 'png' | 'pdf' | 'svg';
  width?: number;
  height?: number;
  backgroundColor?: string;
  padding?: number | [number, number, number, number];
  quality?: number;     // 图片质量 0-1
}

// 主题配置接口
export interface ThemeConfig {
  mode: 'light' | 'dark';
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    shadow: string;
    // 节点颜色
    nodeBackground: string;
    nodeHeaderBackground: string;
    nodeBorder: string;
    nodeText: string;
    nodeFieldText: string;
    // 边颜色
    edgeStroke: string;
    edgeActiveStroke: string;
    edgeLabel: string;
    // 状态颜色
    hover: string;
    active: string;
    selected: string;
  };
}
