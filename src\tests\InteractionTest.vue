<template>
  <div class="interaction-test">
    <h2>G6图谱交互功能测试</h2>
    
    <div class="test-controls">
      <a-space wrap>
        <a-button type="primary" @click="runAllTests">运行所有测试</a-button>
        <a-button @click="testZoomFunctions">测试缩放功能</a-button>
        <a-button @click="testPanFunctions">测试平移功能</a-button>
        <a-button @click="testViewFunctions">测试视图功能</a-button>
        <a-button @click="loadTestData">加载测试数据</a-button>
        <a-button @click="clearResults">清空结果</a-button>
      </a-space>
    </div>

    <div class="test-results" v-if="testResults.length > 0">
      <h3>测试结果</h3>
      <div class="result-list">
        <div 
          v-for="(result, index) in testResults" 
          :key="index"
          :class="['result-item', result.status]"
        >
          <span class="result-icon">
            {{ result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⏳' }}
          </span>
          <span class="result-text">{{ result.message }}</span>
          <span class="result-time">{{ result.timestamp }}</span>
        </div>
      </div>
    </div>

    <div class="graph-container">
      <LineageGraph
        ref="testGraphRef"
        :data="testGraphData"
        :width="800"
        :height="500"
        @graph-ready="handleGraphReady"
        @node-click="handleNodeClick"
        @edge-click="handleEdgeClick"
        @canvas-click="handleCanvasClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import LineageGraph from '@/components/LineageGraph.vue'
import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data } from '@/utils/graphDataTransform'
import type { G6GraphData } from '@/types/lineage'

// 响应式数据
const testGraphRef = ref<any>(null)
const testGraphData = ref<G6GraphData | null>(null)
const isGraphReady = ref(false)

interface TestResult {
  message: string
  status: 'pass' | 'fail' | 'running'
  timestamp: string
}

const testResults = ref<TestResult[]>([])

// 添加测试结果
const addTestResult = (message: string, status: 'pass' | 'fail' | 'running') => {
  testResults.value.push({
    message,
    status,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 清空测试结果
const clearResults = () => {
  testResults.value = []
}

// 加载测试数据
const loadTestData = () => {
  try {
    const sampleData = createSampleLineageData()
    testGraphData.value = transformToG6Data(sampleData)
    addTestResult('测试数据加载成功', 'pass')
    message.success('测试数据已加载')
  } catch (error) {
    addTestResult(`测试数据加载失败: ${error}`, 'fail')
    message.error('测试数据加载失败')
  }
}

// 测试缩放功能
const testZoomFunctions = async () => {
  if (!isGraphReady.value || !testGraphRef.value) {
    addTestResult('图谱未就绪，无法测试缩放功能', 'fail')
    return
  }

  addTestResult('开始测试缩放功能...', 'running')

  try {
    // 测试放大
    const initialZoom = testGraphRef.value.getCurrentZoom()
    testGraphRef.value.zoomIn()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const zoomAfterIn = testGraphRef.value.getCurrentZoom()
    if (zoomAfterIn > initialZoom) {
      addTestResult('放大功能测试通过', 'pass')
    } else {
      addTestResult('放大功能测试失败', 'fail')
    }

    // 测试缩小
    testGraphRef.value.zoomOut()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const zoomAfterOut = testGraphRef.value.getCurrentZoom()
    if (zoomAfterOut < zoomAfterIn) {
      addTestResult('缩小功能测试通过', 'pass')
    } else {
      addTestResult('缩小功能测试失败', 'fail')
    }

    // 测试指定缩放
    testGraphRef.value.zoomTo(1.5)
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const finalZoom = testGraphRef.value.getCurrentZoom()
    if (Math.abs(finalZoom - 1.5) < 0.1) {
      addTestResult('指定缩放功能测试通过', 'pass')
    } else {
      addTestResult('指定缩放功能测试失败', 'fail')
    }

  } catch (error) {
    addTestResult(`缩放功能测试异常: ${error}`, 'fail')
  }
}

// 测试平移功能
const testPanFunctions = async () => {
  if (!isGraphReady.value || !testGraphRef.value) {
    addTestResult('图谱未就绪，无法测试平移功能', 'fail')
    return
  }

  addTestResult('开始测试平移功能...', 'running')

  try {
    // 测试相对平移
    testGraphRef.value.translateBy(50, 50)
    await new Promise(resolve => setTimeout(resolve, 500))
    addTestResult('相对平移功能测试通过', 'pass')

    // 测试绝对平移
    testGraphRef.value.translateTo(0, 0)
    await new Promise(resolve => setTimeout(resolve, 500))
    addTestResult('绝对平移功能测试通过', 'pass')

  } catch (error) {
    addTestResult(`平移功能测试异常: ${error}`, 'fail')
  }
}

// 测试视图功能
const testViewFunctions = async () => {
  if (!isGraphReady.value || !testGraphRef.value) {
    addTestResult('图谱未就绪，无法测试视图功能', 'fail')
    return
  }

  addTestResult('开始测试视图功能...', 'running')

  try {
    // 测试适应视图
    testGraphRef.value.fitView()
    await new Promise(resolve => setTimeout(resolve, 500))
    addTestResult('适应视图功能测试通过', 'pass')

    // 测试重置视图
    testGraphRef.value.resetView()
    await new Promise(resolve => setTimeout(resolve, 500))
    addTestResult('重置视图功能测试通过', 'pass')

    // 测试获取视图中心
    const center = testGraphRef.value.getViewCenter()
    if (center && typeof center.x === 'number' && typeof center.y === 'number') {
      addTestResult('获取视图中心功能测试通过', 'pass')
    } else {
      addTestResult('获取视图中心功能测试失败', 'fail')
    }

  } catch (error) {
    addTestResult(`视图功能测试异常: ${error}`, 'fail')
  }
}

// 运行所有测试
const runAllTests = async () => {
  clearResults()
  addTestResult('开始运行所有交互功能测试...', 'running')
  
  if (!testGraphData.value) {
    loadTestData()
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  await testZoomFunctions()
  await testPanFunctions()
  await testViewFunctions()
  
  addTestResult('所有测试完成', 'pass')
  message.success('所有交互功能测试完成')
}

// 图谱事件处理
const handleGraphReady = (graph: any) => {
  isGraphReady.value = true
  addTestResult('图谱初始化完成，可以开始测试', 'pass')
  console.log('Test graph ready:', graph)
}

const handleNodeClick = (nodeId: string, nodeData: any) => {
  addTestResult(`节点点击事件触发: ${nodeId}`, 'pass')
  console.log('Node clicked:', nodeId, nodeData)
}

const handleEdgeClick = (edgeId: string, edgeData: any) => {
  addTestResult(`边点击事件触发: ${edgeId}`, 'pass')
  console.log('Edge clicked:', edgeId, edgeData)
}

const handleCanvasClick = () => {
  addTestResult('画布点击事件触发', 'pass')
  console.log('Canvas clicked')
}

// 组件挂载时自动加载测试数据
onMounted(() => {
  loadTestData()
})
</script>

<style scoped>
.interaction-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

.test-results {
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.result-list {
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-icon {
  margin-right: 8px;
  font-size: 16px;
}

.result-text {
  flex: 1;
  margin-right: 8px;
}

.result-time {
  font-size: 12px;
  color: #999;
}

.result-item.pass {
  color: #52c41a;
}

.result-item.fail {
  color: #ff4d4f;
}

.result-item.running {
  color: #1890ff;
}

.graph-container {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background: #fafafa;
}
</style>
