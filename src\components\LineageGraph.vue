<template>
  <div class="lineage-graph" ref="containerRef">
    <!-- G6图谱将在这里渲染 -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Graph } from '@antv/g6'
import { useLineageStore } from '@/stores/lineageStore'
import type { G6GraphData } from '@/types/lineage'
import { registerAllGraphElements } from '@/utils/registerFieldNode'

// Props
interface Props {
  width?: number
  height?: number
  data?: G6GraphData | null
}

const props = withDefaults(defineProps<Props>(), {
  width: 800,
  height: 600,
  data: null
})

// Emits
const emit = defineEmits<{
  graphReady: [graph: any]
  nodeClick: [nodeId: string, nodeData: any]
  edgeClick: [edgeId: string, edgeData: any]
  canvasClick: []
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const graphInstance = ref<any>(null)
const isReady = ref(false)
const resizeObserver = ref<ResizeObserver | null>(null)

// Store
const lineageStore = useLineageStore()

// 转换数据格式为G6 v5格式
const transformData = (data: G6GraphData) => {
  return {
    nodes: data.nodes.map(node => ({
      id: node.id,
      data: {
        ...node
      }
    })),
    edges: data.edges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      data: {
        ...edge
      }
    }))
  }
}

// 初始化图谱
const initGraph = async () => {
  if (!containerRef.value) {
    console.warn('Graph container not found')
    return
  }

  try {
    // 注册自定义表节点和字段级边
    registerAllGraphElements()

    // 创建图谱实例配置
    const config: any = {
      container: containerRef.value,
      width: props.width,
      height: props.height,
      // 布局配置
      layout: {
        type: 'antv-dagre',
        rankdir: 'LR', // 从左到右
        align: 'UL',
        nodesep: 80,
        ranksep: 150,
        controlPoints: true
      },
      // 默认节点样式 - 使用自定义表节点
      node: {
        type: 'table-node',
        style: {
          fill: '#ffffff',
          stroke: '#d9d9d9',
          lineWidth: 1,
          radius: 6
        }
      },
      // 默认边样式 - 使用字段级边
      edge: {
        type: 'field-edge',
        style: {
          stroke: '#1890ff',
          lineWidth: 2,
          endArrow: {
            path: 'M 0,0 L 8,4 L 8,-4 Z',
            fill: '#1890ff'
          },
          labelText: (d: any) => d.data?.lineageEdge?.label || d.data?.label || '',
          labelFill: '#666',
          labelFontSize: 10,
          labelBackground: {
            fill: '#ffffff',
            padding: [2, 4],
            radius: 2
          }
        }
      },
      // 交互行为配置
      behaviors: [
        {
          type: 'drag-canvas',
          enable: true,
          enableOptimize: true,
          allowDragOnItem: false
        },
        {
          type: 'zoom-canvas',
          enable: true,
          enableOptimize: true,
          sensitivity: 1,
          minZoom: 0.1,
          maxZoom: 5,
          fixSelectedItems: {
            fixAll: false,
            fixLineWidth: false,
            fixLabel: false,
            fixState: 'selected'
          }
        },
        {
          type: 'drag-element',
          enable: true,
          enableTransient: true,
          updateComboStructure: false,
          enableDelegate: true,
          delegateStyle: {
            fillOpacity: 0.8,
            fill: '#1890ff',
            stroke: '#1890ff'
          }
        },
        {
          type: 'click-select',
          enable: true,
          multiple: true,
          trigger: 'shift'
        }
      ],
      // 动画配置
      animation: true
    }

    // 如果有数据，添加到配置中
    if (props.data) {
      config.data = transformData(props.data)
    }

    graphInstance.value = new Graph(config)

    // 绑定事件
    bindEvents()

    // 渲染图谱
    graphInstance.value.render()

    isReady.value = true
    emit('graphReady', graphInstance.value)

    console.log('G6 graph initialized successfully')
  } catch (error) {
    console.error('Failed to initialize G6 graph:', error)
  }
}

// 绑定事件
const bindEvents = () => {
  if (!graphInstance.value) return

  // 节点点击事件
  graphInstance.value.on('node:click', (evt: any) => {
    const nodeId = evt.itemId || evt.target?.id
    if (nodeId) {
      const nodeData = graphInstance.value?.getNodeData(nodeId)
      emit('nodeClick', nodeId, nodeData)
    }
  })

  // 边点击事件
  graphInstance.value.on('edge:click', (evt: any) => {
    const edgeId = evt.itemId || evt.target?.id
    if (edgeId) {
      const edgeData = graphInstance.value?.getEdgeData(edgeId)
      emit('edgeClick', edgeId, edgeData)
    }
  })

  // 画布点击事件
  graphInstance.value.on('canvas:click', () => {
    emit('canvasClick')
  })
}

// 设置图谱数据
const setGraphData = (data: G6GraphData) => {
  if (!graphInstance.value || !data) return

  try {
    // 转换数据格式
    const g6Data = transformData(data)

    // 设置新数据
    graphInstance.value.setData(g6Data)

    // 渲染图谱
    graphInstance.value.render()

    // 自适应画布
    nextTick(() => {
      if (graphInstance.value) {
        graphInstance.value.fitView()
      }
    })

    console.log('Graph data updated:', data)
  } catch (error) {
    console.error('Failed to set graph data:', error)
  }
}

// 调整图谱尺寸
const resizeGraph = (width: number, height: number) => {
  if (!graphInstance.value) return

  try {
    graphInstance.value.setSize(width, height)
    graphInstance.value.fitView()
  } catch (error) {
    console.error('Failed to resize graph:', error)
  }
}

// 缩放控制方法
const zoomTo = (ratio: number, center?: { x: number; y: number }) => {
  if (!graphInstance.value) return

  try {
    if (center) {
      graphInstance.value.zoomTo(ratio, center)
    } else {
      graphInstance.value.zoomTo(ratio)
    }
  } catch (error) {
    console.error('Failed to zoom graph:', error)
  }
}

const zoomIn = () => {
  if (!graphInstance.value) return

  try {
    const currentZoom = graphInstance.value.getZoom()
    const newZoom = Math.min(currentZoom * 1.2, 5) // 最大缩放5倍
    graphInstance.value.zoomTo(newZoom)
  } catch (error) {
    console.error('Failed to zoom in:', error)
  }
}

const zoomOut = () => {
  if (!graphInstance.value) return

  try {
    const currentZoom = graphInstance.value.getZoom()
    const newZoom = Math.max(currentZoom / 1.2, 0.1) // 最小缩放0.1倍
    graphInstance.value.zoomTo(newZoom)
  } catch (error) {
    console.error('Failed to zoom out:', error)
  }
}

// 平移控制方法
const translateTo = (x: number, y: number) => {
  if (!graphInstance.value) return

  try {
    graphInstance.value.translateTo(x, y)
  } catch (error) {
    console.error('Failed to translate graph:', error)
  }
}

const translateBy = (dx: number, dy: number) => {
  if (!graphInstance.value) return

  try {
    graphInstance.value.translate(dx, dy)
  } catch (error) {
    console.error('Failed to translate graph:', error)
  }
}

// 适应视图
const fitView = (padding?: number | number[]) => {
  if (!graphInstance.value) return

  try {
    const fitPadding = padding || 20
    graphInstance.value.fitView(fitPadding)
  } catch (error) {
    console.error('Failed to fit view:', error)
  }
}

// 重置视图
const resetView = () => {
  if (!graphInstance.value) return

  try {
    graphInstance.value.zoomTo(1)
    graphInstance.value.translateTo(0, 0)
    graphInstance.value.fitView()
  } catch (error) {
    console.error('Failed to reset view:', error)
  }
}

// 获取当前缩放比例
const getCurrentZoom = (): number => {
  if (!graphInstance.value) return 1

  try {
    return graphInstance.value.getZoom()
  } catch (error) {
    console.error('Failed to get current zoom:', error)
    return 1
  }
}

// 获取当前视图中心点
const getViewCenter = (): { x: number; y: number } => {
  if (!graphInstance.value) return { x: 0, y: 0 }

  try {
    const { width, height } = graphInstance.value.getSize()
    return { x: width / 2, y: height / 2 }
  } catch (error) {
    console.error('Failed to get view center:', error)
    return { x: 0, y: 0 }
  }
}

// 销毁图谱
const destroyGraph = () => {
  if (graphInstance.value) {
    graphInstance.value.destroy()
    graphInstance.value = null
    isReady.value = false
  }
}

// 设置响应式监听
const setupResizeObserver = () => {
  if (!containerRef.value || !window.ResizeObserver) return

  resizeObserver.value = new ResizeObserver((entries) => {
    for (const entry of entries) {
      const { width, height } = entry.contentRect
      if (width > 0 && height > 0 && isReady.value) {
        resizeGraph(width, height)
      }
    }
  })

  resizeObserver.value.observe(containerRef.value)
}

// 清理响应式监听
const cleanupResizeObserver = () => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
    resizeObserver.value = null
  }
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (newData && isReady.value) {
      setGraphData(newData)
    }
  },
  { deep: true }
)

// 监听尺寸变化
watch(
  () => [props.width, props.height],
  ([newWidth, newHeight]) => {
    if (isReady.value) {
      resizeGraph(newWidth, newHeight)
    }
  }
)

// 生命周期
onMounted(() => {
  nextTick(() => {
    initGraph().then(() => {
      // 图谱初始化完成后设置响应式监听
      setupResizeObserver()
    })
  })
})

onUnmounted(() => {
  cleanupResizeObserver()
  destroyGraph()
})

// 暴露方法给父组件
defineExpose({
  graph: graphInstance,
  isReady,
  setGraphData,
  resizeGraph,
  destroyGraph,
  // 交互控制方法
  zoomTo,
  zoomIn,
  zoomOut,
  translateTo,
  translateBy,
  fitView,
  resetView,
  getCurrentZoom,
  getViewCenter
})
</script>

<style scoped>
.lineage-graph {
  width: 100%;
  height: 100%;
  position: relative;
  background: #fafafa;
  border-radius: 4px;
  overflow: hidden;
}
</style>
